// import { defineConfig } from 'vitepress'
import { with<PERSON><PERSON>ma<PERSON> } from "vitepress-plugin-mermaid";

// https://vitepress.dev/reference/site-config
export default withMermaid({
  title: "ERPNext 知识库",
  description: "面向业务人员的 ERPNext 使用指南和最佳实践",
  head: [
    // 最常用的 favicon (兼容性最佳)
    ['link', { rel: 'mask-icon', href: '/favicon.svg', color: '#1a73e8' }],
    ['link', { rel: 'icon', href: '/favicon.svg', type: 'image/svg+xml' }],
  ],

  themeConfig: {
    // https://vitepress.dev/reference/default-theme-config
    nav: [
      { text: '首页', link: '/' },
      { text: '快速入门', link: '/quick-start/what-is-erpnext' },
      { text: '实施指南', link: '/implementation-guide/' },
      { text: '模块说明', link: '/modules/' },
      { text: '常见问题', link: '/faq/' }
    ],

    sidebar: {
      '/quick-start/': [
        {
          text: '快速入门',
          collapsed: false,
          items: [
            { text: 'ERPNext 是什么？', link: '/quick-start/what-is-erpnext' },
          ]
        }
      ],
      '/implementation-guide/': [
        {
          text: '实施指南',
          items: [
            { text: '项目准备', link: '/implementation-guide/project-preparation' },
            { text: '系统配置', link: '/implementation-guide/system-configuration' },
            { text: '数据迁移', link: '/implementation-guide/data-migration' },
            { text: '用户培训', link: '/implementation-guide/user-training' },
            { text: '上线支持', link: '/implementation-guide/go-live-support' },
          ]
        }
      ],
      '/modules/': [
        {
          text: '模块说明',
          collapsed: false,
          items: [
            {
              text: '销售管理',
              link: '/modules/sales',
              // items: [
              //   { text: '销售订单', link: '/modules/sales/order' },
              //   { text: '销售发票', link: '/modules/sales/invoice' },
              //   { text: '客户管理', link: '/modules/sales/customer' }
              // ]
            },
            {
              text: '采购管理',
              link: '/modules/procurement',
              // items: [
              //   { text: '采购订单', link: '/modules/procurement/order' },
              //   { text: '采购发票', link: '/modules/procurement/invoice' },
              //   { text: '供应商管理', link: '/modules/procurement/supplier' }
              // ]
            },
            {
              text: '库存管理',
              link: '/modules/inventory',
              // items: [
              //   { text: '库存盘点', link: '/modules/inventory/stock' },
              //   { text: '仓库管理', link: '/modules/inventory/warehouse' },
              //   { text: '物料管理', link: '/modules/inventory/item' }
              // ]
            },
            {
              text: '财务会计',
              link: '/modules/accounting',
              // items: [
              //   { text: '总账', link: '/modules/accounting/gl' },
              //   { text: '应收应付', link: '/modules/accounting/ar-ap' },
              //   { text: '财务报表', link: '/modules/accounting/reports' }
              // ]
            },
            {
              text: '生产制造',
              link: '/modules/manufacturing',
              // items: [
              //   { text: '生产订单', link: '/modules/manufacturing/order' },
              //   { text: 'BOM管理', link: '/modules/manufacturing/bom' },
              //   { text: '工艺路线', link: '/modules/manufacturing/routing' }
              // ]
            },
            {
              text: '人力资源',
              link: '/modules/hr',
              // items: [
              //   { text: '员工管理', link: '/modules/hr/employee' },
              //   { text: '考勤管理', link: '/modules/hr/attendance' },
              //   { text: '薪资管理', link: '/modules/hr/payroll' }
              // ]
            },
            {
              text: '项目管理',
              link: '/modules/projects',
              // items: [
              //   { text: '项目计划', link: '/modules/projects/plan' },
              //   { text: '任务管理', link: '/modules/projects/task' },
              //   { text: '项目报表', link: '/modules/projects/reports' }
              // ]
            },
            {
              text: '客户关系',
              link: '/modules/crm',
              // items: [
              //   { text: '销售线索', link: '/modules/crm/lead' },
              //   { text: '商机管理', link: '/modules/crm/opportunity' },
              //   { text: '客户服务', link: '/modules/crm/service' }
              // ]
            }
          ]
        }
      ],
      '/faq/': [
        {
          text: '常见问题',
          items: [
            // 可以在这里添加具体的FAQ链接
            { text: 'Mermaid 图表示例', link: '/faq/mermaid-demo' },
            { text: '批号管理', link: '/faq/batch-management' },
            { text: '企业经营关键会计分录速览表', link: '/faq/key-accounting-entries' },
          ]
        }
      ],
      '/docs/': [
        {
          text: '文档',
          items: [
            // 可以在这里添加具体的FAQ链接
            { text: '无大纲页面', link: '/docs/no-outline' },
            { text: '单标题页面', link: '/docs/single-title' },
            { text: '有大纲页面', link: '/docs/with-outline' },
            { text: '自定义大纲显示', link: '/docs/select-title' },
          ]
        }
      ]
    },
    outline: {
      // outline: false, // 完全禁用大纲
      level: [2, 6],
      label: '页面导航',
    },

    // socialLinks: [
    //   { icon: 'github', link: 'https://github.com/vuejs/vitepress' } // 您可以替换成您的项目链接
    // ]
    search: {
      provider: 'local',
      options: {
        translations: {
          button: {
            buttonText: '搜索文档',
            buttonAriaLabel: '搜索文档'
          },
          modal: {
            noResultsText: '无法找到相关结果',
            resetButtonTitle: '清除查询条件',
            footer: {
              selectText: '选择',
              navigateText: '切换',
              closeText: '关闭'
            }
          }
        }
      }
    },
    footer: {
      // message: 'ERPNext 实施知识库',
      copyright: 'Copyright © 2025 ERPNext 知识库'
    }
  },
  // optionally, you can pass MermaidConfig
  mermaid: {
    // refer https://mermaid.js.org/config/setup/modules/mermaidAPI.html#mermaidapi-configuration-defaults for options

    // --- 全局配置 ---
    // interactive: true, // 开启放大缩小和平移功能！
    theme: 'forest',   // 设置主题为 'forest'
    logLevel: 'warn',  // 设置日志级别为 'warn'
    fontFamily: 'Roboto, sans-serif', // 设置自定义字体

    // --- 图表类型特定配置 ---
    flowchart: {
      curve: 'linear', // 流程图的连接线为直线
      htmlLabels: true // 确保流程图使用 HTML 标签
    },
    sequence: {
      noteAlign: 'left'  // 笔记左对齐
    },
    gantt: {
      axisFormat: '%Y-%m-%d'   // 甘特图坐标轴日期格式
    },
  },
  // optionally set additional config for plugin itself with MermaidPluginConfig
  mermaidPlugin: {
    class: "mermaid my-class", // set additional css classes for parent container
  },
})
