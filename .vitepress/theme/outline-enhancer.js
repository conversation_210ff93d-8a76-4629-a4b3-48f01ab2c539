// .vitepress/theme/outline-enhancer.js
// 这个脚本提供更精确的大纲检测和控制

export function setupOutlineEnhancer() {
  // 等待 DOM 加载完成
  if (typeof window === 'undefined') return
  
  function checkAndToggleOutline() {
    const doc = document.querySelector('.VPDoc')
    const aside = document.querySelector('.VPDocAside')
    const main = document.querySelector('.VPDocMain')
    const outlineItems = document.querySelectorAll('.VPDocOutlineItem')
    
    if (!doc) return
    
    // 检查是否有大纲项目
    const hasOutlineItems = outlineItems.length > 0
    
    console.log('检查大纲:', {
      hasOutlineItems,
      outlineItemsCount: outlineItems.length,
      docExists: !!doc,
      asideExists: !!aside,
      mainExists: !!main
    })
    
    if (hasOutlineItems) {
      // 有大纲内容，移除所有隐藏类
      doc.classList.remove('no-outline', 'force-no-outline')
      doc.setAttribute('data-outline-count', outlineItems.length)
      
      // 确保 aside 可见
      if (aside) {
        aside.style.display = ''
        aside.style.visibility = ''
        aside.style.opacity = ''
      }
      
      console.log('显示大纲，大纲项目数:', outlineItems.length)
    } else {
      // 没有大纲内容
      doc.classList.add('no-outline', 'force-no-outline')
      doc.setAttribute('data-outline-count', '0')
      
      console.log('隐藏大纲，扩展主内容区域')
      
      // 强制应用样式
      if (aside) {
        aside.style.display = 'none'
      }
      
      if (main) {
        const container = main.querySelector('.container')
        const content = main.querySelector('.content')
        
        if (container) {
          container.style.maxWidth = 'none'
          container.style.width = '100%'
          container.style.padding = '0 48px'
        }
        
        if (content) {
          content.style.maxWidth = '1280px'
          content.style.width = '100%'
          content.style.margin = '0 auto'
        }
      }
    }
    
    // 触发重绘
    doc.offsetHeight
  }
  
  // 初始检查
  function initCheck() {
    // 延迟检查，确保 VitePress 完全渲染
    setTimeout(checkAndToggleOutline, 100)
  }
  
  // 页面加载时检查
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initCheck)
  } else {
    initCheck()
  }
  
  // 路由变化时重新检查（VitePress SPA 路由）
  let lastPath = location.pathname
  const observer = new MutationObserver(() => {
    if (location.pathname !== lastPath) {
      lastPath = location.pathname
      setTimeout(checkAndToggleOutline, 200) // 给 VitePress 时间渲染新页面
    }
  })
  
  // 监听页面内容变化
  observer.observe(document.body, {
    childList: true,
    subtree: true
  })
  
  // 监听窗口大小变化
  window.addEventListener('resize', checkAndToggleOutline)
  
  // 返回清理函数
  return () => {
    observer.disconnect()
    window.removeEventListener('resize', checkAndToggleOutline)
  }
}

// 自动执行
if (typeof window !== 'undefined') {
  setupOutlineEnhancer()
}