// .vitepress/theme/debug.js
// 调试脚本，帮助诊断布局问题

export function setupDebugMode() {
  if (typeof window === 'undefined') return
  
  // 添加调试控制台
  function addDebugConsole() {
    const debugPanel = document.createElement('div')
    debugPanel.id = 'vp-debug-panel'
    debugPanel.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      background: rgba(0,0,0,0.9);
      color: white;
      padding: 15px;
      border-radius: 8px;
      font-size: 11px;
      z-index: 9999;
      max-width: 400px;
      font-family: 'Courier New', monospace;
      max-height: 80vh;
      overflow-y: auto;
      line-height: 1.4;
    `
    
    const toggleButton = document.createElement('button')
    toggleButton.textContent = 'Debug Layout'
    toggleButton.style.cssText = `
      position: fixed;
      top: 10px;
      right: 10px;
      z-index: 10000;
      padding: 8px 12px;
      background: #007acc;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
    `
    
    let debugVisible = false
    
    toggleButton.onclick = () => {
      debugVisible = !debugVisible
      if (debugVisible) {
        debugPanel.style.display = 'block'
        document.body.classList.add('debug-layout')
        updateDebugInfo()
      } else {
        debugPanel.style.display = 'none'
        document.body.classList.remove('debug-layout')
      }
    }
    
    function getElementInfo(selector) {
      const el = document.querySelector(selector)
      if (!el) return { exists: false }
      
      const computed = getComputedStyle(el)
      const rect = el.getBoundingClientRect()
      
      return {
        exists: true,
        display: computed.display,
        width: computed.width,
        maxWidth: computed.maxWidth,
        margin: computed.margin,
        padding: computed.padding,
        position: computed.position,
        left: computed.left,
        right: computed.right,
        rectWidth: Math.round(rect.width),
        rectLeft: Math.round(rect.left),
        rectRight: Math.round(rect.right),
        classes: el.className,
        tagName: el.tagName
      }
    }
    
    function updateDebugInfo() {
      const elements = {
        '.VPDoc': getElementInfo('.VPDoc'),
        '.VPDocMain': getElementInfo('.VPDocMain'),
        '.VPDocAside': getElementInfo('.VPDocAside'),
        '.VPDocMain .container': getElementInfo('.VPDocMain .container'),
        '.VPDocMain .content': getElementInfo('.VPDocMain .content'),
        '.container': getElementInfo('.container'),
        '.content': getElementInfo('.content'),
        '.vp-doc': getElementInfo('.vp-doc')
      }
      
      const outlineItems = document.querySelectorAll('.VPDocOutlineItem')
      
      let html = `
        <div style="margin-bottom: 10px;">
          <strong>页面信息</strong><br>
          URL: ${window.location.pathname}<br>
          大纲项目数: ${outlineItems.length}<br>
          视口宽度: ${window.innerWidth}px<br>
        </div>
      `
      
      Object.entries(elements).forEach(([selector, info]) => {
        html += `<div style="margin-bottom: 8px; border-left: 3px solid ${info.exists ? '#4CAF50' : '#f44336'}; padding-left: 8px;">
          <strong>${selector}</strong><br>`
        
        if (info.exists) {
          html += `
            显示: ${info.display}<br>
            宽度: ${info.width} (实际: ${info.rectWidth}px)<br>
            最大宽度: ${info.maxWidth}<br>
            边距: ${info.margin}<br>
            内距: ${info.padding}<br>
            位置: left=${info.rectLeft}px, right=${info.rectRight}px<br>
            类名: ${info.classes || '(无)'}<br>
          `
        } else {
          html += `<span style="color: #ff6b6b;">元素不存在</span><br>`
        }
        
        html += `</div>`
      })
      
      // 添加控制按钮
      html += `
        <div style="margin-top: 15px; padding-top: 10px; border-top: 1px solid #666;">
          <button onclick="window.inspectLayout()" style="background: #28a745; color: white; border: none; padding: 5px 10px; margin: 2px; border-radius: 3px; cursor: pointer; font-size: 11px;">检查布局</button>
          <button onclick="window.toggleOutlineForce(false)" style="background: #dc3545; color: white; border: none; padding: 5px 10px; margin: 2px; border-radius: 3px; cursor: pointer; font-size: 11px;">隐藏大纲</button>
          <button onclick="window.toggleOutlineForce(true)" style="background: #007bff; color: white; border: none; padding: 5px 10px; margin: 2px; border-radius: 3px; cursor: pointer; font-size: 11px;">显示大纲</button>
        </div>
      `
      
      debugPanel.innerHTML = html
    }
    
    document.body.appendChild(toggleButton)
    document.body.appendChild(debugPanel)
    debugPanel.style.display = 'none'
    
    // 定期更新调试信息
    setInterval(() => {
      if (debugVisible) updateDebugInfo()
    }, 1000)
  }
  
  // 布局检查函数
  window.inspectLayout = function() {
    console.group('🔍 VitePress 布局检查')
    
    const selectors = [
      '.VPDoc',
      '.VPDocMain', 
      '.VPDocAside',
      '.container',
      '.content',
      '.vp-doc'
    ]
    
    selectors.forEach(selector => {
      const els = document.querySelectorAll(selector)
      console.group(`${selector} (找到 ${els.length} 个)`)
      
      els.forEach((el, index) => {
        const computed = getComputedStyle(el)
        const rect = el.getBoundingClientRect()
        
        console.log(`[${index}]`, {
          element: el,
          computed: {
            display: computed.display,
            width: computed.width,
            maxWidth: computed.maxWidth,
            margin: computed.margin,
            padding: computed.padding
          },
          rect: {
            width: rect.width,
            left: rect.left,
            right: rect.right
          },
          classes: el.className
        })
      })
      
      console.groupEnd()
    })
    
    console.groupEnd()
  }
  
  // 强制切换大纲显示/隐藏的测试函数
  window.toggleOutlineForce = function(show) {
    const doc = document.querySelector('.VPDoc')
    if (!doc) {
      console.error('找不到 .VPDoc 元素')
      return
    }
    
    if (show) {
      doc.classList.remove('no-outline', 'force-no-outline')
      console.log('✅ 强制显示大纲')
    } else {
      doc.classList.add('no-outline', 'force-no-outline')
      console.log('❌ 强制隐藏大纲，扩展主内容')
      
      // 额外的强制样式应用
      setTimeout(() => {
        const main = document.querySelector('.VPDocMain')
        const containers = document.querySelectorAll('.container')
        const contents = document.querySelectorAll('.content')
        
        console.log('🔧 强制应用样式:', {
          main: !!main,
          containers: containers.length,
          contents: contents.length
        })
        
        // 直接设置内联样式作为最后手段
        containers.forEach(container => {
          container.style.maxWidth = 'none'
          container.style.width = '100%'
          container.style.paddingLeft = '48px'
          container.style.paddingRight = '48px'
        })
        
        contents.forEach(content => {
          content.style.maxWidth = '1280px'
          content.style.width = '100%'
          content.style.margin = '0 auto'
        })
        
      }, 100)
    }
    
    // 触发调试信息更新
    if (document.body.classList.contains('debug-layout')) {
      setTimeout(() => {
        const event = new Event('click')
        document.querySelector('#vp-debug-panel + button')?.dispatchEvent(event)
      }, 200)
    }
  }
  
  // 页面加载完成后添加调试面板
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', addDebugConsole)
  } else {
    addDebugConsole()
  }
  
  // 在控制台输出有用的调试信息
  console.log('🚀 VitePress Debug Mode activated. Use:')
  console.log('- inspectLayout() 检查页面布局结构')
  console.log('- toggleOutlineForce(false) 强制隐藏大纲')
  console.log('- toggleOutlineForce(true) 强制显示大纲')
  console.log('- 点击右上角 "Debug Layout" 按钮查看实时信息')
}