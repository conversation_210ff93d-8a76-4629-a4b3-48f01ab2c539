// .vitepress/theme/index.js
import DefaultTheme from 'vitepress/theme'
import './custom.css'
import { setupOutlineEnhancer } from './outline-enhancer.js'
import { setupDebugMode } from './debug.js'
import svgPanZoom from 'svg-pan-zoom'

export default {
  extends: DefaultTheme,
  enhanceApp({ app, router, siteData }) {
    // 设置大纲增强功能
    if (typeof window !== 'undefined') {
      setupOutlineEnhancer()
      
      // 开发环境下启用调试模式
      // if (import.meta.env.DEV) {
      //   setupDebugMode()
      // }

      // 在路由变化后（页面加载或切换）执行
      router.onAfterRouteChanged = () => {
        const applyPanZoom = (svg: SVGElement) => {
          if (!(svg as any).dataset.panZoomApplied) {
            try {
              const panZoom = svgPanZoom(svg, {
                zoomEnabled: true,
                panEnabled: true,
                controlIconsEnabled: false,
                fit: true,
                center: true,
              });
              panZoom.resize();
              panZoom.fit();
              panZoom.center();
              (svg as any).dataset.panZoomApplied = 'true';
            } catch (e) {
              console.error('Failed to apply svg-pan-zoom:', e);
            }
          }
        };

        // 先处理已存在的
        document.querySelectorAll('.mermaid svg').forEach(element => {
          applyPanZoom(element as SVGElement);
        });

        // 监听后续插入
        const observer = new MutationObserver(mutations => {
          mutations.forEach(mutation => {
            mutation.addedNodes.forEach(node => {
              if (
                node.nodeType === 1 &&
                (node as Element).matches &&
                (node as Element).matches('.mermaid svg')
              ) {
                applyPanZoom(node as SVGElement);
              }
              // 递归查找子节点
              if (node.nodeType === 1) {
                (node as Element).querySelectorAll &&
                  (node as Element).querySelectorAll('.mermaid svg').forEach(svg => {
                    applyPanZoom(svg as SVGElement);
                  });
              }
            });
          });
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true,
        });

        // Mermaid 弹窗功能
        document.querySelectorAll('.mermaid').forEach(container => {
          // 避免重复绑定
          if ((container as any)._popupBound) return;
          (container as any)._popupBound = true;

          container.addEventListener('click', e => {
            const svg = container.querySelector('svg');
            if (!svg) return;

            const backdrop = document.createElement('div');
            backdrop.className = 'mermaid-modal-backdrop';

            const modal = document.createElement('div');
            modal.className = 'mermaid-modal-content';

            const closeBtn = document.createElement('span');
            closeBtn.className = 'mermaid-modal-close';
            closeBtn.innerHTML = '&times;';
            closeBtn.onclick = () => document.body.removeChild(backdrop);

            // 克隆 SVG
            const svgClone = svg.cloneNode(true) as SVGElement;
            if (!svgClone.getAttribute('viewBox') && svgClone.hasAttribute('width') && svgClone.hasAttribute('height')) {
              const w = svgClone.getAttribute('width');
              const h = svgClone.getAttribute('height');
              svgClone.setAttribute('viewBox', `0 0 ${w} ${h}`);
            }
            svgClone.removeAttribute('style');
            svgClone.removeAttribute('width');
            svgClone.removeAttribute('height');

            modal.appendChild(closeBtn);
            modal.appendChild(svgClone);
            backdrop.appendChild(modal);

            // 弹窗内SVG支持缩放
            setTimeout(() => {
              try {
                const panZoom = svgPanZoom(svgClone, {
                  zoomEnabled: true,
                  panEnabled: true,
                  controlIconsEnabled: true,
                  fit: true,
                  center: true,
                  minZoom: 0.2,
                  maxZoom: 10,
                });
                panZoom.resize();
                panZoom.fit();
                panZoom.center();
              } catch (e) {
                console.error('Failed to apply svg-pan-zoom in modal:', e);
              }
            }, 0);

            backdrop.addEventListener('click', ev => {
              if (ev.target === backdrop) document.body.removeChild(backdrop);
            });

            document.body.appendChild(backdrop);
          });
        });
      }
    }
  }
}