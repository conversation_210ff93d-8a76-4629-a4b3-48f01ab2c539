# Mermaid 图表示例

本页面展示了在 VitePress 中使用 Mermaid 图表的各种示例。

## 流程图 (Flowchart)

```mermaid
graph LR
    A[开始] --> B{是否有库存?}
    B -->|是| C[创建销售订单]
    B -->|否| D[创建采购申请]
    C --> E[创建发货单]
    D --> F[创建采购订单]
    F --> G[接收物料]
    G --> C
    E --> H[结束]
```

## 时序图 (Sequence Diagram)

```mermaid
sequenceDiagram
    participant 客户
    participant 销售
    participant 仓库
    participant 财务
    
    客户->>销售: 下订单
    销售->>仓库: 创建发货单
    仓库->>销售: 确认发货
    销售->>财务: 创建发票
    财务->>客户: 发送发票
    客户->>财务: 支付
    财务->>销售: 确认收款
```

## 甘特图 (Gantt Chart)

```mermaid
gantt
    title ERPNext 实施计划
    dateFormat YYYY-MM-DD
    
    section 准备阶段
    需求分析        :a1, 2023-01-01, 30d
    系统设计        :a2, after a1, 20d
    
    section 实施阶段
    系统安装        :b1, after a2, 5d
    基础配置        :b2, after b1, 10d
    数据迁移        :b3, after b2, 15d
    
    section 上线阶段
    用户培训        :c1, after b3, 10d
    系统测试        :c2, after c1, 7d
    正式上线        :c3, after c2, 3d
```

## 类图 (Class Diagram)

```mermaid
classDiagram
    class 公司{
        +String 名称
        +String 税号
        +创建()
        +更新()
    }
    class 客户{
        +String 名称
        +String 联系人
        +String 电话
        +创建订单()
    }
    class 供应商{
        +String 名称
        +String 联系人
        +创建采购订单()
    }
    class 产品{
        +String 名称
        +Number 价格
        +Number 库存
        +更新库存()
    }
    
    公司 "1" -- "n" 客户: 服务
    公司 "1" -- "n" 供应商: 采购
    客户 "1" -- "n" 产品: 购买
    供应商 "1" -- "n" 产品: 提供
```

## 状态图 (State Diagram)

```mermaid
stateDiagram-v2
    [*] --> 草稿
    草稿 --> 已提交: 提交
    已提交 --> 已批准: 批准
    已提交 --> 已拒绝: 拒绝
    已批准 --> 已完成: 完成
    已拒绝 --> 草稿: 修改
    已完成 --> [*]
```

 

## 使用提示

1. 在 Markdown 文件中，使用 \```mermaid 和 \``` 包裹 Mermaid 代码
2. 确保代码块内的内容不要缩进，否则可能导致渲染失败
3. 图表类型包括：graph（流程图）、sequenceDiagram（时序图）、gantt（甘特图）、classDiagram（类图）、stateDiagram-v2（状态图）、pie（饼图）等
4. 更多语法和示例请参考 [Mermaid 官方文档](https://mermaid.js.org/)
