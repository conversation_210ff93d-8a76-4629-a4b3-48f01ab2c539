
## 订单中保存与提交概念讲解



### **销售订单状态（以销售订单为例）**

1.  **草稿 (Draft)：**
    *   **含义：** 这是您刚刚创建或编辑单据，但尚未最终确认的状态。
    *   **特点：** **可以随意修改所有字段。** 您可以修改客户信息、添加/删除物料、调整数量、单价等。
    *   **影响：** 不对库存、会计账务等产生任何影响。它只是一个临时的业务记录。
    *   **类比：** 就像您手写了一份草稿合同，内容还在推敲，可以随时涂改。

2.  **已提交 (Submitted)：**
    *   **含义：** 您已经对单据内容进行了最终确认，并将其提交给系统。
    *   **特点：** **一旦提交，该单据就变得不可直接修改。** 绝大部分字段都会被锁定。
    *   **影响：**
        *   **业务逻辑：** 订单数量会被锁定，可用于后续的生产计划、发货计划等。
        *   **库存预留：** 如果在物料设置中开启了“按销售订单预留库存”，那么提交后，这部分物料就会被预留起来，不能被其他订单随意分配。
        *   **会计分录：** **销售订单本身通常不直接生成会计分录**（除非有特殊配置，例如预收款的录入），但它是后续发货单和销售发票生成的基础。
    *   **类比：** 就像您和客户已经正式签署了一份合同。合同内容一旦签字盖章，就不能随便涂改了，因为它具有法律效力，并会影响到双方的后续行为。

### **对“已提交”单据的修改方式：**

您提到的“修订”在ERPNext中通常通过 **“更正 (Amend)”** 或 **“取消 (Cancel)”** 来实现。

1.  **更正 (Amend)：**
    *   **目的：** 当您需要对已提交的单据进行**小范围的修改**时（例如，数量略微调整、增加一行备注、修改发货日期等），且这个修改是**业务允许**的。
    *   **操作：**
        1.  打开已提交的销售订单。
        2.  在单据顶部的工具栏中，找到并点击 **“更正 (Amend)”** 按钮（或在“创建”/“操作”下拉菜单中）。
        3.  系统会生成一个原单据的**可编辑副本**，并自动将原单据的状态更新为“已更正 (Amended)”。
        4.  在新的副本中进行修改。
        5.  保存并提交这个新的副本。
    *   **影响：**
        *   系统会**取消原单据**产生的业务影响（例如库存预留），然后根据新的更正后的单据**重新产生影响**。
        *   如果原单据已经产生了后续单据（如发货单），在更正时系统会提示您，甚至可能要求您先取消后续单据才能更正。
        *   **审计：** 更正功能会保留历史记录，可以看到原始单据和更正后的单据，方便追溯。
    *   **类比：** 就像对已签署的合同进行“补充协议”或“修订版本”，新版本生效，但原版本记录仍在。

2.  **取消 (Cancel)：**
    *   **目的：** 当您需要**彻底作废**一个已提交的单据时。
    *   **操作：**
        1.  打开已提交的销售订单。
        2.  在单据顶部的工具栏中，找到并点击 **“取消 (Cancel)”** 按钮。
        3.  系统会提示您确认。
    *   **影响：**
        *   单据状态变为“已取消 (Cancelled)”。
        *   **所有业务影响被回滚**：例如，如果之前有库存预留，预留会被释放。
        *   **会计分录（如果该类型单据会生成分录）：** 会生成冲销分录，抵消原有的会计影响。
        *   **后续单据：** 如果该销售订单已经生成了发货单或销售发票，您通常需要**先取消后续单据**，才能取消原始的销售订单。
    *   **类比：** 就像您和客户彻底“解除”了合同，所有相关约定都失效。

### **软木布生产企业建议：**

*   **明确提交职责：** 只有最终确认无误的销售订单才应该被提交。
*   **培训员工：** 确保销售和订单处理的员工清楚“草稿”、“提交”、“更正”和“取消”的含义和影响，以及何时使用哪种操作。
*   **流程规范：** 建议制定一个内部流程，规定什么情况下可以更正，什么情况下需要取消，以及更正/取消的审批流程。特别是在涉及修改金额或数量时，可能需要主管或财务部门的审批。
*   **谨慎操作：** 尤其是在订单已经发货或开票后，对销售订单进行更正或取消会涉及到更多后续单据的联动操作，需要更加谨慎。

ERPNext的设计正是为了提供这样的严谨性和追溯性，确保数据准确性。