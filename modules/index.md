# ERPNext 模块说明

ERPNext 是一个功能完整的企业资源规划系统，包含多个核心业务模块。本章节将详细介绍各个模块的功能和应用场景。

## 核心模块

### 销售管理
- 客户管理：维护客户信息、信用额度、价格表等
- 销售订单：处理客户订单、跟踪订单状态
- 销售发票：生成客户发票、管理应收账款

### 采购管理
- 供应商管理：维护供应商信息、评估供应商表现
- 采购订单：处理采购申请、跟踪采购进度
- 采购发票：处理供应商发票、管理应付账款

### 库存管理
- 物料管理：维护物料主数据、BOM结构
- 仓库管理：管理多个仓库、库位
- 库存操作：入库、出库、调拨、盘点等

### 财务会计
- 总账：记录所有财务交易
- 应收应付：管理客户和供应商账款
- 财务报表：生成资产负债表、利润表等

### 生产制造
- 生产订单：管理生产计划、跟踪生产进度
- BOM管理：维护产品结构、物料清单
- 工艺路线：定义生产工序、工时定额

### 人力资源
- 员工管理：维护员工信息、组织架构
- 考勤管理：记录考勤、请假、加班
- 薪资管理：计算工资、处理社保公积金

### 项目管理
- 项目计划：制定项目计划、分配资源
- 任务管理：跟踪任务进度、管理工时
- 项目报表：分析项目成本、进度等

## 模块集成

ERPNext 的各个模块紧密集成，数据实时同步，确保业务流程的连贯性和数据的准确性。例如：

- 销售订单自动生成采购需求
- 采购收货自动更新库存
- 生产领料自动扣减库存
- 销售发货自动生成应收账款
- 采购收货自动生成应付账款

## 扩展功能

除了核心模块外，ERPNext 还提供：

- 客户关系管理(CRM)
- 资产管理
- 质量管理
- 服务管理
- 网站管理
- 知识库管理

这些功能可以根据企业需求选择性启用，满足不同行业和规模企业的管理需求。
