# Markdown Extension Examples

This page demonstrates some of the built-in markdown extensions provided by VitePress.

## Syntax Highlighting

VitePress provides Syntax Highlighting powered by [<PERSON><PERSON>](https://github.com/shikijs/shiki), with additional features like line-highlighting:


 主要任务

```mermaid
flowchart LR
    Start --> Stop
```

```mermaid
graph LR;
    K([...........<img src='/vitepress-plugin-mermaid/K.png' width='60' >...........])-.->G((...........<img id='git' src='/vitepress-plugin-mermaid/Octocat.png' width='50' >...........));
    H([...........<img id='helm' src='/vitepress-plugin-mermaid/helm.png' width='60' >...........])-.->G
    G-->A;
    A(...........<img src='/vitepress-plugin-mermaid/argo-cd.png' width='60' >...........)-->D(...........<img src='/vitepress-plugin-mermaid/ocp.png' width='60' >...........);
classDef img fill:none,color:transparent,stroke:none,borderRadius:50px
class G,D,A,K,H,B img
click K "https://kustomize.io/" _blank
click G "http://www.github.com" "This is a link" _blank
```


**Input**

````md
```js{4}
export default {
  data () {
    return {
      msg: 'Highlighted!'
    }
  }
}
```
````

**Output**

```js{4}
export default {
  data () {
    return {
      msg: 'Highlighted!'
    }
  }
}
```

## Custom Containers

**Input**

```md
::: info
This is an info box.
:::

::: tip
This is a tip.
:::

::: warning
This is a warning.
:::

::: danger
This is a dangerous warning.
:::

::: details
This is a details block.
:::
```

**Output**

::: info
This is an info box.
:::

::: tip
This is a tip.
:::

::: warning
This is a warning.
:::

::: danger
This is a dangerous warning.
:::

::: details
This is a details block.
:::

## More

Check out the documentation for the [full list of markdown extensions](https://vitepress.dev/guide/markdown).
