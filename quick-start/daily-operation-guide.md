
# ERPNext 日常运营指南 - 部门视角 (v15)

## 核心原则：

1.  **主动查阅：** 每个部门的员工应主动登录ERPNext，查看与自己职责相关的待办事项和信息。
2.  **状态驱动：** 关注系统单据的状态变化，这是任务触发的信号。
3.  **信息共享：** 通过系统记录所有操作，确保信息的透明和可追溯。
4.  **分级审批：** 发票类单据采用“制单-审核-生效”的工作流，确保财务数据的准确性。

---

## 1. 销售部 (Sales Department)

销售部的核心职责是获取订单、跟踪订单履约进度，最终确认发货和**发起开票**。

**日常关注点：**

*   **新订单：** 是否有客户提交了新的销售意向或订单。
*   **订单进展：** 已提交的销售订单处于什么阶段（待处理、待发货、已发货）。
*   **出库单状态：** 跟踪销售出库单（Delivery Note）的状态，特别是仓库是否已备货、发货。
*   **销售发票状态：** 销售发票草稿是否已提交给会计，是否已确认生效。

**从系统获取信息 / 执行操作：**

| 时间 | 模块/功能 | 关注信息/操作 | 目的/协作信息 |
| :--- | :-------- | :------------ | :------------ |
| **上午/全天** | **销售订单 (Sales Order)** | **列表视图：** <br> - 过滤`状态`为“提交”或“打开”的新订单。<br> - 检查`预计库存数量`，初步判断是否满足客户需求。<br> **单个订单：** <br> - 查看关联的`发货单 (Delivery Note)`状态。 | **新订单录入：** 客户下单后，立即创建并提交销售订单。<br>**库存预判：** 初步判断是否可以满足客户交期。<br>**订单跟踪：** 了解订单进展，为客户提供信息。 |
| | **发货单 (Delivery Note)** | **列表视图：** <br> - 过滤`状态`为“草稿”且`来自销售部`的单据（自己创建）。<br> - 过滤`状态`为“待发货”或“待确认”的单据（仓库操作后）。<br>**单个发货单：** <br> - 查看仓库是否已提交（表示已备货/发货）。 | **出库触发：** 销售订单提交后，**立即创建发货单草稿并保存**，这是给仓库的备货指令。<br>**发货确认：** 仓库发货后，销售应**确认发货单，并使其状态变为“已发货”**。此步骤很重要，是开票依据。 |
| | **销售发票 (Sales Invoice)** | **列表视图：** <br> - 过滤`状态`为“草稿”的单据（自己创建）。<br> - 过滤`状态`为“待提交”或“待确认”的单据。<br>**单个发票：** <br> - 查看会计部是否已确认。 | **开票操作：** 当发货单状态变为“已发货”后，销售部门应根据发货单**创建销售发票草稿并保存**。<br>**提交审核：** **将销售发票草稿提交给会计部审核确认。**<br>**进度跟踪：** 跟踪发票的确认状态。 |
| | **报表：** <br> - `销售订单报表` <br> - `发货单报表` <br> - `销售发票报表` | - 跟踪特定客户或产品的订单、发货及开票情况。<br> - 检查销售业绩和完成率。 | 宏观掌握销售数据。 |

**与其它部门的协作：**

*   **对仓库：** 提交销售订单并创建发货单草稿，是给仓库的备货指令。当仓库发货后，销售负责最终确认发货单。
*   **对生产（当前是仓库代劳）：** 间接沟通。当发现销售订单无法满足（无现货），且需要生产时，销售应及时向内部沟通（当前可能直接和仓库沟通，因为仓库负责物料出入库）。
*   **对会计部：** **创建并提交销售发票草稿**，等待会计部审核确认。

---

## 2. 仓库部 (Warehouse Department)

仓库部的核心职责是管理实物库存，执行物料的入库、出库、调拨，并为生产提供物料支持。

**日常关注点：**

*   **销售出库：** 哪些销售订单的发货单草稿已提交，需要备货和发货。
*   **原材料入库：** 哪些采购订单的物料已到货，需要收货入库。
*   **生产用料：** 生产部门需要哪些原材料，需要出库给他们。
*   **成品入库：** 生产部门完成了哪些成品，需要收货入库。
*   **库存准确性：** 实时库存是否与实物一致。

**从系统获取信息 / 执行操作：**

| 时间 | 模块/功能 | 关注信息/操作 | 目的/协作信息 |
| :--- | :-------- | :------------ | :------------ |
| **上午** | **发货单 (Delivery Note)** | **列表视图：** <br> - 过滤`状态`为“草稿”且`来自销售部`（或根据流程约定）的单据。<br> - 优先处理期望交货期近的单据。 | **备货指令：** 销售创建的发货单草稿是仓库的备货和发货指令。<br>**优先级识别：** 根据交期合理安排备货顺序。 |
| | **库存总览 (Stock Balance)** | **报表/列表：** <br> - 查找需要备货的产品，检查其`实际库存`是否充足。 | **库存核实：** 确认是否有足够库存满足发货需求。<br>**问题反馈：** 如果库存不足，及时通知销售或生产（当前流程）。 |
| | **采购收货单 (Purchase Receipt)** | **列表视图：** <br> - 过滤`状态`为“草稿”或“待提交”的单据。<br> - 检查是否有已到货的采购物料待收货。 | **收货入库：** 根据供应商送货单，创建并提交采购收货单，使原材料入库。<br>**库存更新：** 确保系统库存与实物同步。 |
| **全天** | **库存凭证 (Stock Entry)** | **新建库存凭证：** <br> - **原材料出库给生产：** <br>   类型：`材料发出` (Material Issue)<br>   目的：可自定义为“生产领用”或“生产消耗”<br>   源仓库：原材料仓库<br>   目标仓库：生产车间（虚拟仓库）或直接不填。<br> - **成品从生产入库：** <br>   类型：`材料接收` (Material Receipt)<br>   目的：可自定义为“生产入库”或“成品入库”<br>   源仓库：生产车间（虚拟仓库）或不填。<br>   目标仓库：成品仓库。 | **生产物料管理：** 记录原材料出库和成品入库，是当前没有生产模块时的替代方案。<br>**库存调整：** 确保生产过程中的物料流动在系统中得到体现。 |
| | **发货单 (Delivery Note)** | **操作：** <br> - 找到已备货完成的发货单草稿。<br> - 点击`提交 (Submit)`，使其状态变为“已发货”（或“待销售确认”）。 | **完成发货：** 告知销售和系统，货物已完成备货并发出。<br>**触发下游：** 提交后，销售才能进行最终确认。 |
| | **报表：** <br> - `库存分类账 (Stock Ledger)`<br> - `库存概览 (Stock Balance)` | - 跟踪特定物料的进出明细。<br> - 查看所有仓库的实时库存情况。 | **库存核对：** 随时了解库存动态，进行盘点和核对。 |

**与其它部门的协作：**

*   **对销售：** 收到发货单草稿后，执行备货和发货操作，并在系统内提交发货单。
*   **对生产：** 接收生产部门的原材料领用请求，进行库存凭证的“材料发出”操作。接收生产部门的成品入库通知，进行库存凭证的“材料接收”操作。
*   **对采购：** 接收采购到货的物料，进行采购收货入库。

---

## 3. 生产部 (Production Department)

在尚未启用生产模块的阶段，生产部门更多的是通过线下或简单的电子表格管理生产计划和进度。其与ERPNext的互动主要通过**仓库部**进行物料的出入库记录。

**日常关注点：**

*   **生产任务：** 今日/本周需要生产哪些软木布，数量是多少。
*   **原材料需求：** 生产这些软木布需要哪些原材料，目前是否充足。
*   **生产进度：** 各个生产订单的完成情况。
*   **成品产出：** 哪些成品已生产完成，可以入库。

**从系统获取信息 / 执行操作（主要通过与仓库部的沟通）：**

| 时间 | 模块/功能 | 关注信息/操作 | 目的/协作信息 |
| :--- | :-------- | :------------ | :------------ |
| **上午/全天** | **（ERPNext中无直接模块，主要靠沟通）** | - 从销售部或生产计划员处获得生产任务。<br> - **与仓库部沟通：** <br>   - 告知所需原材料种类和数量，请求仓库出库。<br>   - 通知成品已生产完成，请求仓库入库。 | **获取任务：** 明确生产目标。<br>**物料保障：** 确保生产所需原材料及时到位。<br>**成果交付：** 告知系统（通过仓库）成品已产出。 |
| | **报表：** <br> - `库存总览 (Stock Balance)` | - 偶尔查看关键原材料和成品的库存状态，了解大致情况。 | **信息参考：** 辅助生产决策，但具体操作由仓库执行。 |

**与其它部门的协作：**

*   **对仓库：** 明确提出原材料领用需求，并通知成品生产完成，以便仓库进行系统出入库操作。
*   **对销售：** 告知成品生产完成，以便销售部门安排发货。
*   **对生产计划（如果存在）：** 接收生产任务，并反馈生产进度。

---

## 4. 采购部 (Procurement Department)

采购部的核心职责是确保生产和销售所需的原材料及时供应，管理供应商关系，并**发起采购发票**。

**日常关注点：**

*   **物料需求：** 哪些原材料库存不足，需要采购。
*   **采购订单状态：** 已发出的采购订单的交货进度。
*   **供应商发票状态：** 采购发票草稿是否已提交给会计，是否已确认生效。

**从系统获取信息 / 执行操作：**

| 时间 | 模块/功能 | 关注信息/操作 | 目的/协作信息 |
| :--- | :-------- | :------------ | :------------ |
| **上午** | **库存总览 (Stock Balance)** | **报表/列表：** <br> - 过滤原材料，关注`实际库存`和`再订购水平 (Reorder Level)`。<br> - 识别低于再订购水平的物料。 | **主动采购：** 根据库存预警，主动发起采购以避免停产或断货。<br>**需求确认：** 确认生产或仓库提出的物料需求。 |
| | **采购订单 (Purchase Order)** | **列表视图：** <br> - 过滤`状态`为“提交”但“未完成”的订单。<br> - 关注`预计收货日期`，并催促供应商。 | **订单跟踪：** 掌握已下单物料的到货进度。<br>**供应商管理：** 及时与供应商沟通，确保按期到货。 |
| **全天** | **采购订单 (Purchase Order)** | **新建采购订单：** <br> - 根据物料需求，选择供应商，创建并提交采购订单。 | **发起采购：** 正式向供应商下达采购指令。 |
| | **采购收货单 (Purchase Receipt)** | **列表视图：** <br> - 查看仓库是否已收到货并提交了采购收货单。 | **收货确认：** 确认物料已到达并入库，这是后续开票的依据。 |
| | **采购发票 (Purchase Invoice)** | **新建采购发票：** <br> - 根据`采购收货单`或供应商发票，**创建采购发票草稿并保存**。<br>**提交审核：** **将采购发票草稿提交给会计部审核确认。** | **录入供应商账单：** 记录应付账款的初步信息。<br>**提交审核：** 等待会计部确认后，账单才正式生效。 |
| | **报表：** <br> - `物料要求报表` (如果启用)<br> - `供应商采购历史报表` | - 查看整体物料需求。<br> - 分析供应商表现。 | **策略制定：** 辅助采购决策和供应商选择。 |

**与其它部门的协作：**

*   **对仓库：** 告知采购订单的预计到货时间，并关注仓库是否已收到货并提交了采购收货单。
*   **对生产（当前是仓库代劳）：** 确保生产所需原材料的供应，若有紧急需求，直接沟通。
*   **对会计部：** **创建并提交采购发票草稿**，等待会计部审核确认。

---

## 5. 会计部 (Accounting Department) 

会计部的核心职责是管理公司财务数据，确保账务准确，进行收付款管理，以及各类财务报告。

**日常关注点：**

*   **待确认发票：** 销售部提交的销售发票草稿和采购部提交的采购发票草稿。
*   **应收账款：** 哪些销售发票已生效，但尚未收款。
*   **应付账款：** 哪些采购发票已生效，但尚未付款。
*   **银行对账：** 银行流水与系统记录是否一致。

**从系统获取信息 / 执行操作：**

| 时间 | 模块/功能 | 关注信息/操作 | 目的/协作信息 |
| :--- | :-------- | :------------ | :------------ |
| **上午/全天** | **销售发票 (Sales Invoice)** | **列表视图：** <br> - 过滤`状态`为“草稿”或“待提交”的单据（来自销售部）。<br>**单个发票：** <br> - 审核发票金额、税率、关联的销售订单和发货单等。<br> - **确认无误后，点击`提交 (Submit)`按钮，使发票生效。** | **收入确认：** 审核并正式确认销售收入。<br>**应收账款生成：** 发票提交后，应收账款将生成。 |
| | **采购发票 (Purchase Invoice)** | **列表视图：** <br> - 过滤`状态`为“草稿”或“待提交”的单据（来自采购部）。<br>**单个发票：** <br> - 审核发票金额、税率、关联的采购订单和采购收货单等。<br> - **确认无误后，点击`提交 (Submit)`按钮，使发票生效。** | **费用确认：** 审核并正式确认采购费用。<br>**应付账款生成：** 发票提交后，应付账款将生成。 |
| | **收款凭证 (Payment Entry)** | **新建收款凭证：** <br> - 根据客户付款，创建收款凭证，关联销售发票。<br>**列表视图：** <br> - 过滤待处理的收款。 | **管理应收：** 记录客户回款，核销应收账款。 |
| | **付款凭证 (Payment Entry)** | **新建付款凭证：** <br> - 根据供应商付款，创建付款凭证，关联采购发票。<br>**列表视图：** <br> - 过滤待处理的付款。 | **管理应付：** 记录对外付款，核销应付账款。 |
| | **总账 (General Ledger)** | **报表：** <br> - `总账报告`<br> - `应收账款汇总报表 (Accounts Receivable Summary)`<br> - `应付账款汇总报表 (Accounts Payable Summary)` | **财务核对：** 随时查看公司资产、负债、收入、费用情况。<br>**风险识别：** 关注逾期应收/应付账款。 |

**与其它部门的协作：**

*   **对销售：** 接收销售发票草稿并进行审核确认。如果发现问题，需退回销售部门修改。
*   **对采购：** 接收采购发票草稿并进行审核确认。如果发现问题，需退回采购部门修改。
*   **对各部门：** 提供准确的财务数据支持决策。

---

## 总结

现在ERPNext流程中引入了发票的“草稿-审核-生效”机制，使得财务数据的准确性和控制性得到了加强：

*   **销售部**和**采购部**作为业务发起方，负责将原始交易信息准确录入为发票**草稿**。
*   **会计部**作为财务审核方，拥有最终确认发票并使其在系统中**生效**的权限。

这种分权制衡的模式，确保了业务操作的高效性，同时维护了财务数据的严谨性。未来启动生产模块时，`生产订单 (Work Order)`、`物料请求 (Material Request)`和`工艺路线 (Routing)`将进一步提升生产管理的精细度。